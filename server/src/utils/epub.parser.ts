import * as epubParser from "epub-parser";
import * as cheerio from 'cheerio';
import { basename, join } from 'path';
import { writeFileSync } from 'fs';

export class EpubParser {
  private path: string = null
  private imageDir: string = './upload/epub/images'

  constructor(path: string) {
    this.path = path;
  }

  parse() {
    return new Promise((resolve, reject) => {
      epubParser.open(this.path, (err, data) => {
        if(err) return reject(err);

        const chapters = this.getChapters(data);
        resolve(chapters);
      })
    })
  }

  getChapters(data) {
    const chapters = [];
    const navHtml = !data.easy.isEpub3 ? data.easy.navMapHTML : data.easy.epub3NavHtml;
    const $nav = cheerio.load(navHtml);
    const _this = this;

    const imagesHref = Object.keys(data.easy.itemHashByHref).filter(e => e.indexOf('.png') > -1 || e.indexOf('.jpg') > -1);
    imagesHref.forEach(e => {
      const imagePath = join(_this.imageDir, basename(e));
      const binary = epubParser.extractBinary(data.paths.opsRoot + e)
      writeFileSync(imagePath, binary, 'binary');
    })

    $nav('a').each(function() {
      const title = $nav(this).text();
      const href = $nav(this).attr('href').split('#')[0];
      const content = _this.getContent(data.paths.opsRoot + href);
      const $ = cheerio.load(content);
      const baseUrl = process.env.BUILD_ENV === 'local' ? 'http://localhost:9015' : 'https://dev.advayta.org'

      $('img').each(function() {
        const src = $(this).attr('src');
        const fileName = basename(src);
        const imagePath = join(_this.imageDir, fileName);
        $(this).attr('src', `${baseUrl}/${imagePath}`);
      })

      chapters.push({ title, content: $.html()})
    })

    return chapters;
  }

  getContent(href: string) {
    return epubParser.extractText(href)
  }
}