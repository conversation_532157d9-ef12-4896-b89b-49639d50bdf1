import {BaseEntity, <PERSON>umn, <PERSON><PERSON>ty, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne, PrimaryGeneratedColumn} from "typeorm";
import {File} from "@/entity/File";

@Entity()
export class Advertising extends BaseEntity {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({nullable: true})
    active: boolean;

    @Column({nullable: true})
    advertising: boolean;

    @Column()
    title: string

    @Column()
    description: string

    @Column()
    link: string
    
    @Column({nullable: true})
    type: string

    @Column({nullable: true})
    freq: number

    @Column('date', { nullable: true })
    date: Date

    @OneToOne(() => File, file => file.advertising, { nullable: true, onDelete: 'SET NULL' })
    @JoinColumn()
    image: File | null
}