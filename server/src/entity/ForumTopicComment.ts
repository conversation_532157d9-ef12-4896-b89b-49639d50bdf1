import {
    <PERSON><PERSON>ty,
    PrimaryGeneratedColumn,
    Column,
    BaseEntity,
    ManyToMany,
    JoinTable, OneToOne, ManyToOne, Join<PERSON><PERSON>umn, CreateDateColumn, UpdateDateColumn, OneToMany
} from 'typeorm';
import {File} from "@/entity/File";
import {User} from "@/entity/User";
import {ForumCategory} from "@/entity/ForumCategory";
import {ForumTopic} from "@/entity/ForumTopic";
import {ForumTopicCommentLike} from "@/entity/ForumTopicCommentLike";
import {ForumTopicCommentFavorite} from "@/entity/ForumTopicCommentFavorite";

@Entity()
export class ForumTopicComment extends BaseEntity {
    @PrimaryGeneratedColumn()
    id: number

    @CreateDateColumn({ name: 'created_at' }) createdAt: Date;
    @UpdateDateColumn({ name: 'updated_at' }) updatedAt: Date;

    @Column({select: false})
    comment: string

    @OneToMany(() => ForumTopicComment, c => c.replyComment, { onDelete: 'CASCADE' })
    reply: ForumTopicComment;

    @ManyToOne(() => ForumTopicComment, c => c.reply, { onDelete: 'CASCADE' })
    @JoinColumn()
    replyComment: ForumTopicComment;

    @ManyToOne(() => ForumTopic, topic => topic.comments, {onDelete: 'CASCADE' })
    @JoinColumn()
    topic: ForumTopic;

    @OneToMany(() => ForumTopicCommentLike, comment => comment.topicComment)
    likes: ForumTopicCommentLike[];

    @OneToMany(() => ForumTopicCommentFavorite, comment => comment.topicComment)
    favorites: ForumTopicCommentFavorite[];

    @ManyToOne(() => User, user => user.forumTopics, { onDelete: 'CASCADE' })
    user: User;
}