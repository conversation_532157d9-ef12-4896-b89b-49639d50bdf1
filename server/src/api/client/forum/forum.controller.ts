import { Body, Controller, Delete, Get, Param, Post, Query, Req, UseGuards } from '@nestjs/common';
import { ForumService } from './forum.service';
import {OptionalJwtAuthGuard} from "@/api/user/guards/auth.optional.guard";
import {User} from "@/api/user/decorators/user.decorator";
import {Auth} from "@/api/user/decorators/auth.decorator";

@Controller('client/forum')
export class ForumController {
  constructor(private readonly forumService: ForumService) {}

  @Get('categories/:id')
  @UseGuards(OptionalJwtAuthGuard)
  async getCategory(
    @Param('id') id: number,
    @User() user: any,
    @Query('page') page: number,
    @Query('sort') sort: string
  ) {
    return await this.forumService.getCategory(id, user, page, 5, sort)
  }

  @Get('categories')
  @UseGuards(OptionalJwtAuthGuard)
  async getCategories(@User() user: any, @Req() req: any) {
    return await this.forumService.getCategories(user)
  }

  @Get('favourites')
  @Auth()
  async getFavourites(@Req() req: any) {
    return this.forumService.getFavourites(req.user.id);
  }

  @Get('topics/:id')
  @UseGuards(OptionalJwtAuthGuard)
  async getTopic(@Param('id') id: number, @User() user: any) {
    return await this.forumService.getTopic(id, user)
  }

  @Post('topics')
  @Auth()
  async createTopic(@Body() body: any, @Req() req) {
    return await this.forumService.createTopic(body, req.user)
  }

  @Delete('topics/:id')
  @Auth()
  async deleteTopic(@Param('id') id: number) {
    return await this.forumService.deleteTopic(id)
  }

  @Post('comments')
  @Auth()
  async saveComment(@Body() body: any, @Req() req: any) {
    return await this.forumService.saveComment(body, req.user)
  }

  @Post('comments/like')
  @Auth()
  async likeComment(@Body('id') id: number, @Req() req: any) {
    return await this.forumService.likeComment(id, req.user)
  }

  @Post('comments/favorite')
  @Auth()
  async favoriteComment(@Body('id') id: number, @Req() req: any) {
    return await this.forumService.favoriteComment(id, req.user)
  }

  @Post('topics/like')
  @Auth()
  async likeTopic(@Body('id') id: number, @Req() req: any) {
    return await this.forumService.likeTopic(id, req.user)
  }

  @Post('topics/favorite')
  @Auth()
  async favoriteTopic(@Body('id') id: number, @Req() req: any) {
    return await this.forumService.favoriteTopic(id, req.user)
  }

  @Delete('comments/:id')
  @Auth()
  async deleteComment(@Param('id') id: number, @Req() req: any) {
    return await this.forumService.deleteComment(id)
  }
}
