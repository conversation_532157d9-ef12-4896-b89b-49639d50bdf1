import { Component, inject, input, output, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { LibraryService } from '@/services/library.service';
import { ProfileService } from '@/services/profile.service';
import { ToasterService } from '@/services/toaster.service';

@Component({
  selector: 'app-book-purchase-modal',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './book-purchase-modal.component.html',
  styleUrl: './book-purchase-modal.component.scss'
})
export class BookPurchaseModalComponent {
  private libraryService = inject(LibraryService);
  private profileService = inject(ProfileService);
  private toasterService = inject(ToasterService);

  // Inputs
  bookData = input.required<any>();
  isVisible = input<boolean>(false);

  // Outputs
  close = output<void>();
  purchaseComplete = output<void>();

  // State
  paymentType = signal<string>('stripe');
  isLoading = signal<boolean>(false);

  onClose() {
    this.close.emit();
  }

  onPurchase() {
    if (!this.profileService.profile) {
      this.toasterService.showToast('Необходимо авторизоваться', 'error', 'bottom-middle');
      return;
    }

    this.isLoading.set(true);

    this.libraryService.purchase(this.bookData().id, this.paymentType()).subscribe({
      next: (res: any) => {
        location.href = res.paymentUrl;
      },
      error: (err) => {
        this.isLoading.set(false);
        this.toasterService.showToast(err.error?.message || 'Ошибка при покупке', 'error', 'bottom-middle');
      }
    });
  }

  onPaymentTypeChange(type: string) {
    this.paymentType.set(type);
  }
}
