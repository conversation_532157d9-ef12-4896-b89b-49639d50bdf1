import { BreadcrumbComponent } from "@/components/breadcrumb/breadcrumb.component";
import { CommonModule, isPlatformBrowser, NgOptimizedImage } from "@angular/common";
import { Component, inject, OnInit, PLATFORM_ID, ViewEncapsulation } from "@angular/core";
import {environment} from "@/env/environment";
import { AdvertisingService } from "@/services/advertising.service";

@Component({
    selector: 'app-news',
    standalone: true,
    imports: [
      CommonModule,
      BreadcrumbComponent,
      NgOptimizedImage
    ],
    encapsulation: ViewEncapsulation.None,  // Отключает изоляцию стилей
    templateUrl: './news.components.html',
    styleUrl: './news.components.scss'
  })
  export class NewsComponent implements OnInit {
    protected readonly environment = environment;
    advertisingService = inject(AdvertisingService);
    platformId = inject(PLATFORM_ID);
   
  
    ngOnInit() {
        if (isPlatformBrowser(this.platformId)) {
            this.advertisingService.getAll().subscribe(
                {
                    next: (res: any) => {
                    this.advertisings = res

                    },
                    error: (err) => {
                        console.error('Error loading advertisements:', err);
                    }
                }
            );
        }
    }

    
  advertisings: any[] = [];

  navigateToLink(link: string) {
    if (isPlatformBrowser(this.platformId)) {
      window.open(link, '_blank');
    }
  }

  onImageError(event: any) {
    // Hide only the image, show placeholder instead
    const imgElement = event.target;
    imgElement.style.display = 'none';

    // Show placeholder if it exists
    const container = imgElement.closest('.ad-image-container');
    if (container) {
      const placeholder = container.querySelector('.ad-image-placeholder');
      if (placeholder) {
        placeholder.style.display = 'flex';
      } else {
        // Create a simple placeholder
        const newPlaceholder = document.createElement('div');
        newPlaceholder.className = 'ad-image-placeholder';
        newPlaceholder.innerHTML = '<span>Изображение недоступно</span>';
        newPlaceholder.style.cssText = 'display: flex; align-items: center; justify-content: center; width: 100%; height: 100%; background: #f0f0f0; color: #666;';
        container.appendChild(newPlaceholder);
      }
    }
  }
}