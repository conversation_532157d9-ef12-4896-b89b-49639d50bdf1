import { HttpClient } from '@angular/common/http';
import {inject, Injectable} from '@angular/core';
import { catchError, EMPTY, tap } from 'rxjs';
import { ShareDataService } from './share-data.service';

@Injectable({
  providedIn: 'root'
})
export class ForumService {
  http = inject(HttpClient);
  shareDataService = inject(ShareDataService)

  getCategory(id: number, page: number, sort = 'dateAsc') {
    return this.http.get(`/client/forum/categories/${id}`, {params: {page, sort}});
  }

  getCategories() {
    return this.http.get('/client/forum/categories', {withCredentials: true});
  }

  getTopic(id: string) {
    return this.http.get(`/client/forum/topics/${id}`);
  }

  createTopic(body: any) {
    return this.http.post(`/client/forum/topics`, body);
  }

  deleteTopic(id: number) {
    return this.http.delete(`/client/forum/topics/${id}`);
  }

  sendComment(body: any) {
    return this.http.post('/client/forum/comments', body);
  }

  likeComment(id: number) {
    return this.http.post(`/client/forum/comments/like`, {id}).pipe(
      tap(),
      catchError(error => {
        this.shareDataService.showInfoModal(error.error.message);
        return EMPTY;
      })
    )
  }

  favoriteComment(id: number) {
    return this.http.post(`/client/forum/comments/favorite`, { id }).pipe(
      tap(),
      catchError(error => {
        this.shareDataService.showInfoModal(error.error.message);
        return EMPTY;
      })
    )
  }

  likeTopic(id: number) {
    return this.http.post(`/client/forum/topics/like`, {id}).pipe(
      tap(),
      catchError(error => {
        this.shareDataService.showInfoModal(error.error.message);
        return EMPTY;
      })
    )
  }

  favoriteTopic(id: number) {
    return this.http.post(`/client/forum/topics/favorite`, {id}).pipe(
      tap(),
      catchError(error => {
        this.shareDataService.showInfoModal(error.error.message);
        return EMPTY;
      })
    )
  }

  removeComment(id: number) {
    return this.http.delete(`/client/forum/comments/${id}`);
  }

  getFavourites() {
    return this.http.get('/client/forum/favourites')
  }
}
